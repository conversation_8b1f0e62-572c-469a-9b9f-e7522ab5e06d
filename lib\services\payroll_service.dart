/// خدمة إدارة الرواتب المتقدمة
/// توفر عمليات حساب وإدارة الرواتب مع التكامل المحاسبي
library;

import '../database/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

import '../services/attendance_service.dart';
import '../services/employee_service.dart';
import '../exceptions/validation_exception.dart';

/// نموذج كشف الراتب
class PayrollRecord {
  final int? id;
  final int employeeId;
  final int month;
  final int year;
  final double basicSalary;
  final double allowances;
  final double overtimePay;
  final double bonuses;
  final double grossSalary;
  final double incomeTax;
  final double socialInsurance;
  final double loanDeductions;
  final double otherDeductions;
  final double totalDeductions;
  final double netSalary;
  final int workingDays;
  final int actualWorkingDays;
  final double totalHours;
  final double overtimeHours;
  final String status;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PayrollRecord({
    this.id,
    required this.employeeId,
    required this.month,
    required this.year,
    required this.basicSalary,
    this.allowances = 0,
    this.overtimePay = 0,
    this.bonuses = 0,
    required this.grossSalary,
    required this.incomeTax,
    required this.socialInsurance,
    this.loanDeductions = 0,
    this.otherDeductions = 0,
    required this.totalDeductions,
    required this.netSalary,
    required this.workingDays,
    required this.actualWorkingDays,
    required this.totalHours,
    this.overtimeHours = 0,
    this.status = 'draft',
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PayrollRecord.fromMap(Map<String, dynamic> map) {
    return PayrollRecord(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      month: map['month'] as int,
      year: map['year'] as int,
      basicSalary: (map['basic_salary'] as num).toDouble(),
      allowances: (map['allowances'] as num).toDouble(),
      overtimePay: (map['overtime_pay'] as num).toDouble(),
      bonuses: (map['bonuses'] as num).toDouble(),
      grossSalary: (map['gross_salary'] as num).toDouble(),
      incomeTax: (map['income_tax'] as num).toDouble(),
      socialInsurance: (map['social_insurance'] as num).toDouble(),
      loanDeductions: (map['loan_deductions'] as num).toDouble(),
      otherDeductions: (map['other_deductions'] as num).toDouble(),
      totalDeductions: (map['total_deductions'] as num).toDouble(),
      netSalary: (map['net_salary'] as num).toDouble(),
      workingDays: map['working_days'] as int,
      actualWorkingDays: map['actual_working_days'] as int,
      totalHours: (map['total_hours'] as num).toDouble(),
      overtimeHours: (map['overtime_hours'] as num).toDouble(),
      status: map['status'] as String,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'employee_id': employeeId,
      'month': month,
      'year': year,
      'basic_salary': basicSalary,
      'allowances': allowances,
      'overtime_pay': overtimePay,
      'bonuses': bonuses,
      'gross_salary': grossSalary,
      'income_tax': incomeTax,
      'social_insurance': socialInsurance,
      'loan_deductions': loanDeductions,
      'other_deductions': otherDeductions,
      'total_deductions': totalDeductions,
      'net_salary': netSalary,
      'working_days': workingDays,
      'actual_working_days': actualWorkingDays,
      'total_hours': totalHours,
      'overtime_hours': overtimeHours,
      'status': status,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة معدلة من كشف الراتب
  PayrollRecord copyWith({
    int? id,
    int? employeeId,
    int? month,
    int? year,
    double? basicSalary,
    double? allowances,
    double? overtimePay,
    double? bonuses,
    double? grossSalary,
    double? incomeTax,
    double? socialInsurance,
    double? loanDeductions,
    double? otherDeductions,
    double? totalDeductions,
    double? netSalary,
    int? workingDays,
    int? actualWorkingDays,
    double? totalHours,
    double? overtimeHours,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PayrollRecord(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      month: month ?? this.month,
      year: year ?? this.year,
      basicSalary: basicSalary ?? this.basicSalary,
      allowances: allowances ?? this.allowances,
      overtimePay: overtimePay ?? this.overtimePay,
      bonuses: bonuses ?? this.bonuses,
      grossSalary: grossSalary ?? this.grossSalary,
      incomeTax: incomeTax ?? this.incomeTax,
      socialInsurance: socialInsurance ?? this.socialInsurance,
      loanDeductions: loanDeductions ?? this.loanDeductions,
      otherDeductions: otherDeductions ?? this.otherDeductions,
      totalDeductions: totalDeductions ?? this.totalDeductions,
      netSalary: netSalary ?? this.netSalary,
      workingDays: workingDays ?? this.workingDays,
      actualWorkingDays: actualWorkingDays ?? this.actualWorkingDays,
      totalHours: totalHours ?? this.totalHours,
      overtimeHours: overtimeHours ?? this.overtimeHours,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من إمكانية التعديل
  bool get canEdit => status == 'draft';

  /// التحقق من إمكانية الاعتماد
  bool get canApprove => status == 'draft';

  /// التحقق من الاعتماد
  bool get isApproved => status == 'approved';

  /// التحقق من الدفع
  bool get isPaid => status == 'paid';
}

class PayrollService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  final AttendanceService _attendanceService = AttendanceService();
  final EmployeeService _employeeService = EmployeeService();

  /// حساب راتب موظف لشهر معين
  Future<PayrollRecord> calculatePayroll({
    required int employeeId,
    required int month,
    required int year,
    double allowances = 0,
    double bonuses = 0,
    double otherDeductions = 0,
    String? notes,
  }) async {
    try {
      // الحصول على بيانات الموظف
      final employee = await _employeeService.getEmployeeById(employeeId);
      if (employee == null) {
        throw ValidationException('الموظف غير موجود');
      }

      // التحقق من عدم وجود كشف راتب لنفس الشهر
      final existingPayroll = await getPayrollRecord(employeeId, month, year);
      if (existingPayroll != null) {
        throw ValidationException('تم إنشاء كشف راتب لهذا الشهر مسبقاً');
      }

      // حساب أيام العمل في الشهر
      final workingDays = _calculateWorkingDaysInMonth(month, year);

      // الحصول على بيانات الحضور للشهر
      final fromDate = DateTime(year, month, 1);
      final toDate = DateTime(year, month + 1, 0);
      final attendanceRecords = await _attendanceService.getAttendanceByPeriod(
        employeeId: employeeId,
        fromDate: fromDate,
        toDate: toDate,
      );

      // حساب الأيام الفعلية وساعات العمل
      final actualWorkingDays = attendanceRecords
          .where((a) => a.status == 'present')
          .length;
      final totalHours = attendanceRecords.fold<double>(
        0,
        (sum, attendance) => sum + attendance.regularHours,
      );
      final overtimeHours = attendanceRecords.fold<double>(
        0,
        (sum, attendance) => sum + attendance.overtimeHours,
      );

      // حساب الراتب الأساسي المتناسب
      final proportionalBasicSalary =
          (employee.basicSalary * actualWorkingDays) / workingDays;

      // حساب أجر الساعات الإضافية
      final hourlyRate =
          employee.basicSalary / (workingDays * 8); // 8 ساعات يومياً
      final overtimePay =
          overtimeHours * hourlyRate * 1.5; // 1.5 ضعف الساعة العادية

      // حساب الراتب الإجمالي
      final grossSalary =
          proportionalBasicSalary + allowances + overtimePay + bonuses;

      // حساب الضرائب والاستقطاعات (تقدير مبسط)
      final taxAmount = grossSalary * 0.1; // 10% ضريبة تقديرية
      final socialInsuranceAmount = grossSalary * 0.07; // 7% ضمان اجتماعي

      // حساب خصم القروض (إن وجدت)
      final loanDeductions = await _calculateLoanDeductions(
        employeeId,
        month,
        year,
      );

      // حساب إجمالي الاستقطاعات
      final totalDeductions =
          taxAmount + socialInsuranceAmount + loanDeductions + otherDeductions;

      // حساب صافي الراتب
      final netSalary = grossSalary - totalDeductions;

      final payrollRecord = PayrollRecord(
        employeeId: employeeId,
        month: month,
        year: year,
        basicSalary: proportionalBasicSalary,
        allowances: allowances,
        overtimePay: overtimePay,
        bonuses: bonuses,
        grossSalary: grossSalary,
        incomeTax: taxAmount,
        socialInsurance: socialInsuranceAmount,
        loanDeductions: loanDeductions,
        otherDeductions: otherDeductions,
        totalDeductions: totalDeductions,
        netSalary: netSalary,
        workingDays: workingDays,
        actualWorkingDays: actualWorkingDays,
        totalHours: totalHours,
        overtimeHours: overtimeHours,
        status: 'draft',
        notes: notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ كشف الراتب
      final db = await _databaseHelper.database;
      final id = await db.insert(
        AppConstants.payrollTable,
        payrollRecord.toMap(),
      );

      final savedRecord = payrollRecord.copyWith(id: id);

      // تسجيل في سجل المراجعة
      await AuditService.log(
        action: 'CREATE',
        entityType: 'PayrollRecord',
        entityId: id,
        description: 'حساب راتب الموظف للشهر $month/$year',
        newValues: savedRecord.toMap(),
      );

      LoggingService.info(
        'تم حساب راتب الموظف بنجاح',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'netSalary': netSalary,
        },
      );

      return savedRecord;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب الراتب',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }

  /// حساب أيام العمل في الشهر (باستثناء الجمعة والسبت)
  int _calculateWorkingDaysInMonth(int month, int year) {
    final lastDay = DateTime(year, month + 1, 0);

    int workingDays = 0;
    for (int day = 1; day <= lastDay.day; day++) {
      final date = DateTime(year, month, day);
      // استثناء الجمعة (5) والسبت (6)
      if (date.weekday != DateTime.friday &&
          date.weekday != DateTime.saturday) {
        workingDays++;
      }
    }

    return workingDays;
  }

  /// حساب خصم القروض للشهر
  Future<double> _calculateLoanDeductions(
    int employeeId,
    int month,
    int year,
  ) async {
    try {
      final db = await _databaseHelper.database;

      // الحصول على الأقساط المستحقة في الشهر المحدد
      final result = await db.rawQuery(
        '''
        SELECT COALESCE(SUM(li.amount), 0) as total_deduction
        FROM ${AppConstants.loanInstallmentsTable} li
        INNER JOIN ${AppConstants.loansTable} l ON li.loan_id = l.id
        WHERE l.employee_id = ?
        AND li.status = 'pending'
        AND strftime('%Y', li.due_date) = ?
        AND strftime('%m', li.due_date) = ?
      ''',
        [employeeId, year.toString(), month.toString().padLeft(2, '0')],
      );

      return (result.first['total_deduction'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب خصم القروض',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      return 0.0;
    }
  }

  /// الحصول على كشف راتب موظف لشهر معين
  Future<PayrollRecord?> getPayrollRecord(
    int employeeId,
    int month,
    int year,
  ) async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.query(
        AppConstants.payrollTable,
        where: 'employee_id = ? AND month = ? AND year = ?',
        whereArgs: [employeeId, month, year],
        limit: 1,
      );

      if (result.isEmpty) return null;
      return PayrollRecord.fromMap(result.first);
    } catch (e) {
      LoggingService.error(
        'خطأ في الحصول على كشف الراتب',
        category: 'PayrollService',
        data: {
          'employeeId': employeeId,
          'month': month,
          'year': year,
          'error': e.toString(),
        },
      );
      rethrow;
    }
  }
}
