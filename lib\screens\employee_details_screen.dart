/// شاشة تفاصيل الموظف
/// عرض شامل لجميع معلومات الموظف مع إمكانية التعديل والإجراءات
library;

import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import 'edit_employee_screen.dart';

class EmployeeDetailsScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeDetailsScreen({
    super.key,
    required this.employee,
  });

  @override
  State<EmployeeDetailsScreen> createState() => _EmployeeDetailsScreenState();
}

class _EmployeeDetailsScreenState extends State<EmployeeDetailsScreen> {
  final EmployeeService _employeeService = EmployeeService();
  late Employee _employee;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _employee = widget.employee;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل الموظف: ${_employee.displayName}'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editEmployee,
            tooltip: 'تعديل الموظف',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'salary',
                child: Row(
                  children: [
                    Icon(Icons.account_balance_wallet),
                    SizedBox(width: 8),
                    Text('حساب الراتب'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'attendance',
                child: Row(
                  children: [
                    Icon(Icons.access_time),
                    SizedBox(width: 8),
                    Text('سجل الحضور'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'loans',
                child: Row(
                  children: [
                    Icon(Icons.money),
                    SizedBox(width: 8),
                    Text('القروض والسلف'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget(message: 'جاري تحديث البيانات...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildEmployeeHeader(),
                  const SizedBox(height: 24),
                  _buildBasicInfoCard(),
                  const SizedBox(height: 16),
                  _buildContactInfoCard(),
                  const SizedBox(height: 16),
                  _buildEmploymentInfoCard(),
                  const SizedBox(height: 16),
                  _buildFinancialInfoCard(),
                  const SizedBox(height: 16),
                  _buildAdditionalInfoCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildEmployeeHeader() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              RevolutionaryColors.damascusSky,
              RevolutionaryColors.damascusSky.withValues(alpha: 0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          children: [
            CircleAvatar(
              radius: 50,
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              backgroundImage: _employee.photoPath != null
                  ? AssetImage(_employee.photoPath!)
                  : null,
              child: _employee.photoPath == null
                  ? const Icon(
                      Icons.person,
                      size: 50,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(height: 16),
            Text(
              _employee.displayName,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'رقم الموظف: ${_employee.employeeNumber}',
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(_employee.status),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                _getStatusText(_employee.status),
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoCard() {
    return _buildInfoCard(
      title: 'المعلومات الأساسية',
      icon: Icons.person,
      children: [
        _buildInfoRow('الاسم الكامل', _employee.fullName),
        _buildInfoRow('الرقم الوطني', _employee.nationalId),
        _buildInfoRow('تاريخ الميلاد', _formatDate(_employee.dateOfBirth)),
        _buildInfoRow('الجنس', _getGenderText(_employee.gender)),
        _buildInfoRow('الحالة الاجتماعية', _getMaritalStatusText(_employee.maritalStatus)),
      ],
    );
  }

  Widget _buildContactInfoCard() {
    return _buildInfoCard(
      title: 'معلومات الاتصال',
      icon: Icons.contact_phone,
      children: [
        _buildInfoRow('رقم الهاتف', _employee.phone ?? 'غير محدد'),
        _buildInfoRow('البريد الإلكتروني', _employee.email ?? 'غير محدد'),
        _buildInfoRow('العنوان', _employee.address ?? 'غير محدد'),
        _buildInfoRow('جهة اتصال الطوارئ', _employee.emergencyContactName ?? 'غير محدد'),
        _buildInfoRow('هاتف الطوارئ', _employee.emergencyContactPhone ?? 'غير محدد'),
      ],
    );
  }

  Widget _buildEmploymentInfoCard() {
    return _buildInfoCard(
      title: 'معلومات التوظيف',
      icon: Icons.work,
      children: [
        _buildInfoRow('تاريخ التوظيف', _formatDate(_employee.hireDate)),
        _buildInfoRow('المنصب', _employee.position ?? 'غير محدد'),
        _buildInfoRow('القسم', _employee.departmentId?.toString() ?? 'غير محدد'),
        _buildInfoRow('نوع التوظيف', _getEmploymentTypeText(_employee.employmentType)),
        _buildInfoRow('الحالة', _getStatusText(_employee.status)),
      ],
    );
  }

  Widget _buildFinancialInfoCard() {
    return _buildInfoCard(
      title: 'المعلومات المالية',
      icon: Icons.account_balance_wallet,
      children: [
        _buildInfoRow('الراتب الأساسي', '${_employee.basicSalary.toStringAsFixed(0)} ل.س'),
        _buildInfoRow('البدلات', '${_employee.allowances.toStringAsFixed(0)} ل.س'),
        _buildInfoRow('رقم الحساب البنكي', _employee.bankAccountNumber ?? 'غير محدد'),
        _buildInfoRow('اسم البنك', _employee.bankName ?? 'غير محدد'),
      ],
    );
  }

  Widget _buildAdditionalInfoCard() {
    return _buildInfoCard(
      title: 'معلومات إضافية',
      icon: Icons.info,
      children: [
        _buildInfoRow('تاريخ الإنشاء', _formatDateTime(_employee.createdAt)),
        _buildInfoRow('آخر تحديث', _formatDateTime(_employee.updatedAt)),
        if (_employee.notes != null && _employee.notes!.isNotEmpty)
          _buildInfoRow('ملاحظات', _employee.notes!),
      ],
    );
  }

  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: RevolutionaryColors.damascusSky),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.employeeStatusActive:
        return RevolutionaryColors.successGlow;
      case AppConstants.employeeStatusInactive:
        return RevolutionaryColors.warningAmber;
      case AppConstants.employeeStatusTerminated:
        return RevolutionaryColors.errorCoral;
      case AppConstants.employeeStatusSuspended:
        return RevolutionaryColors.errorCoral;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case AppConstants.employeeStatusActive:
        return 'نشط';
      case AppConstants.employeeStatusInactive:
        return 'غير نشط';
      case AppConstants.employeeStatusTerminated:
        return 'منتهي الخدمة';
      case AppConstants.employeeStatusSuspended:
        return 'موقوف';
      default:
        return 'غير محدد';
    }
  }

  String _getGenderText(String gender) {
    switch (gender) {
      case AppConstants.genderMale:
        return 'ذكر';
      case AppConstants.genderFemale:
        return 'أنثى';
      default:
        return 'غير محدد';
    }
  }

  String _getMaritalStatusText(String status) {
    switch (status) {
      case AppConstants.maritalStatusSingle:
        return 'أعزب';
      case AppConstants.maritalStatusMarried:
        return 'متزوج';
      case AppConstants.maritalStatusDivorced:
        return 'مطلق';
      case AppConstants.maritalStatusWidowed:
        return 'أرمل';
      default:
        return 'غير محدد';
    }
  }

  String _getEmploymentTypeText(String type) {
    switch (type) {
      case AppConstants.employmentTypeFullTime:
        return 'دوام كامل';
      case AppConstants.employmentTypePartTime:
        return 'دوام جزئي';
      case AppConstants.employmentTypeContract:
        return 'عقد';
      case AppConstants.employmentTypeInternship:
        return 'تدريب';
      default:
        return 'غير محدد';
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _editEmployee() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditEmployeeScreen(employee: _employee),
      ),
    );

    if (result == true) {
      // إعادة تحميل بيانات الموظف
      await _refreshEmployeeData();
    }
  }

  Future<void> _refreshEmployeeData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final updatedEmployee = await _employeeService.getEmployeeById(_employee.id!);
      if (updatedEmployee != null) {
        setState(() {
          _employee = updatedEmployee;
        });
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث بيانات الموظف',
        category: 'EmployeeDetailsScreen',
        data: {'employeeId': _employee.id, 'error': e.toString()},
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'salary':
        _navigateToSalaryCalculation();
        break;
      case 'attendance':
        _navigateToAttendanceRecord();
        break;
      case 'loans':
        _navigateToLoansRecord();
        break;
    }
  }

  void _navigateToSalaryCalculation() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('حساب راتب "${_employee.displayName}" - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _navigateToAttendanceRecord() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سجل حضور "${_employee.displayName}" - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }

  void _navigateToLoansRecord() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('قروض "${_employee.displayName}" - قريباً'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
      ),
    );
  }
}
